package com.k2fsa.sherpa.onnx

import android.content.res.AssetManager
import android.util.Log


/**
 * 单模型ASR引擎 - 参考FunASR的单模型实现
 * 使用一个在线识别器实现预测和最终识别
 */
class SingleModelASREngine(
    private val assetManager: AssetManager,
    private val sampleRateInHz: Int = 16000,
    private val enableSpeakerIdentification: Boolean = true,
    private val speakerThreshold: Float = 0.5f,
    context: android.content.Context? = null
) {

    // 声纹数据持久化管理器
    private var speakerDataManager: SpeakerDataManager? = null
    // 初始化声纹数据管理器
    init {
        if (context != null) {
            speakerDataManager = SpeakerDataManager(context)
        }
    }
    companion object {
        private const val TAG = "SingleModelASREngine"
        
        // FunASR风格的配置参数
        private const val SEGMENT_SIZE = 960 // 60ms at 16kHz
        
        // 预测和最终识别参数 (参考FunASR配置)
        private const val PREDICTION_INTERVAL = 15 // 每15个chunk进行预测
        private const val FINAL_CHUNK_SIZE = 60 // 60个chunk进行最终识别
        private const val MIN_PREDICTION_CHUNKS = 8 // 最少8个chunk才开始预测
        
        // 智能句子边界检测参数
        private const val MIN_SENTENCE_LENGTH = 2
        private const val MAX_PREDICTION_LENGTH = 50
        private val AVOID_SPLIT_PUNCTUATION = setOf('，', '、', ',', ';', '；', '：', ':')
        private val SENTENCE_END_PUNCTUATION = setOf('。', '！', '？', '.', '!', '?')
        private val INCOMPLETE_INDICATORS = setOf('的', '了', '是', '在', '有', '和', '与')
    }

    // 单一识别器实例 (参考FunASR的AutoModel)
    private lateinit var recognizer: OnlineRecognizer
    private lateinit var stream: OnlineStream

    // 说话人识别组件
    private var speakerExtractor: SpeakerEmbeddingExtractor? = null
    private var speakerManager: SpeakerEmbeddingManager? = null
    private var isSpeakerInitialized = false
    
    // 音频缓冲区管理 (参考FunASR的chunks机制)
    private val audioChunks = mutableListOf<FloatArray>()
    
    // 预测计数器和状态 (参考FunASR的cache机制)
    private var predictionCounter = 0
    private var lastPrediction = ""
    private var isInitialized = false
    
    // 端点检测控制
    private var lastEndpointTime = 0L
    private val endpointCooldownMs = 800L
    
    // 音频质量控制
    private var consecutiveEmptyResults = 0
    private val maxConsecutiveEmptyResults = 5  // 增加容错次数
    private var audioQualityScore = 1.0f
    private var lastResetTime = 0L  // 添加重置时间跟踪

    // 累积结果管理
    private var lastFullResult = ""
    private var lastFullPrediction = ""
    
    /**
     * ASR识别结果数据类
     */
    data class ASRResult(
        val text: String,
        val type: ResultType,
        val confidence: Float = 0.0f,
        val timestamp: Long = System.currentTimeMillis(),
        // 说话人识别相关字段
        val speakerName: String = "",
        val speakerConfidence: Float = 0.0f,
        val hasSpeakerInfo: Boolean = false
    )
    
    enum class ResultType {
        PREVIEW,    // 预测结果 (参考FunASR的preview)
        FINAL,      // 最终结果 (参考FunASR的final)
        ENDPOINT    // 端点检测结果
    }


    /**
     * 说话人信息数据类
     */
    data class SpeakerInfo(
        val name: String,
        val confidence: Float,
        val embedding: FloatArray? = null
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false
            other as SpeakerInfo
            if (name != other.name) return false
            if (confidence != other.confidence) return false
            if (embedding != null) {
                if (other.embedding == null) return false
                if (!embedding.contentEquals(other.embedding)) return false
            } else if (other.embedding != null) return false
            return true
        }

        override fun hashCode(): Int {
            var result = name.hashCode()
            result = 31 * result + confidence.hashCode()
            result = 31 * result + (embedding?.contentHashCode() ?: 0)
            return result
        }
    }

    /**
     * ASR监听器接口
     */
    interface ASRListener {
        fun onResult(result: ASRResult)
        fun onError(error: String)
        fun onStatusChanged(status: String)
        fun onSpeakerIdentified(speakerInfo: SpeakerInfo)
        fun onSpeakerRegistered(speakerName: String, success: Boolean)
        fun onSpeakerRemoved(speakerName: String, success: Boolean)
        fun onVadStatusChanged(isSpeech: Boolean)
    }
    
    private var listener: ASRListener? = null
    
    /**
     * 设置监听器
     */
    fun setListener(listener: ASRListener) {
        this.listener = listener
    }
    
    /**
     * 初始化ASR引擎 - 单模型初始化
     */
    fun initialize(): Boolean {
        return try {
            Log.i(TAG, "开始初始化单模型ASR引擎")
            
            // 初始化单一识别器 (参考FunASR的AutoModel)
            initRecognizer()
            Log.i(TAG, "识别器初始化完成")

            // 初始化说话人识别组件
            if (enableSpeakerIdentification) {
                initSpeakerRecognition()
                Log.i(TAG, "说话人识别组件初始化完成")
            }

            // 创建音频流
            stream = recognizer.createStream()
            
            isInitialized = true
            Log.i(TAG, "单模型ASR引擎初始化成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "ASR引擎初始化失败", e)
            listener?.onError("初始化失败: ${e.message}")
            false
        }
    }
    
    /**
     * 处理音频数据 - 单模型核心识别逻辑 (参考FunASR的recognize_chunk)
     */
    fun processAudio(audioData: FloatArray): List<ASRResult> {
        if (!isInitialized) {
            Log.w(TAG, "ASR引擎未初始化")
            return emptyList()
        }

        // 输入验证
        if (audioData.isEmpty() || audioData.size < 160) {
            Log.w(TAG, "输入音频数据无效，跳过处理")
            return emptyList()
        }

        val results = mutableListOf<ASRResult>()

        try {
            // 将音频数据分割为固定大小的片段 (参考FunASR的segment处理)
            val segments = splitAudioIntoSegments(audioData)

            if (segments.isEmpty()) {
                Log.w(TAG, "音频分割后无有效片段")
                return emptyList()
            }

            // Log.d(TAG, "处理音频数据: ${audioData.size}样本 -> ${segments.size}个片段")

            // var endpointDetected = false // 标记是否检测到端点，防止重复处理

            // 处理每个音频片段 (参考FunASR的chunk处理逻辑)
            for (segment in segments) {
                if (segment.isEmpty()) continue

                try {
                    // 添加到缓冲区 (参考FunASR的chunks.append)
                    audioChunks.add(segment)
                    predictionCounter++

                    // 将音频数据送入识别器 (参考FunASR的model.generate)
                    stream.acceptWaveform(segment, sampleRateInHz)

                    // 处理识别器的解码
                    while (recognizer.isReady(stream)) {
                        recognizer.decode(stream)
                    }

                    // 尝试预测识别 (参考FunASR的_try_prediction)
                    val predictionResult = tryPrediction()
                    // Log.i(TAG, "preview1 ${predictionResult?.text}")

                    if (predictionResult != null) {
                        results.add(predictionResult)
                        // 通过监听器发送预测结果
                        listener?.onResult(predictionResult)
                    }

                    // 检查端点检测 (保持SherpaOnnx的智能特性)
                    // 如果已经检测到端点，跳过后续的端点检测
                    // if (!endpointDetected) {
                    //     val endpointResult = tryEndpointDetection()
                    //     if (endpointResult != null) {
                    //         results.add(endpointResult)
                    //         endpointDetected = true
                    //         // 通过监听器发送端点结果
                    //         listener?.onResult(endpointResult)
                    //         Log.d(TAG, "端点检测成功，跳过剩余片段处理")
                    //         break // 端点检测成功后跳出循环
                    //     }
                    // }

                    // 尝试最终识别 (参考FunASR的_try_final_recognition)
                    val finalResult = tryFinalRecognition()
                    if (finalResult != null) {
                        results.add(finalResult)
                        // 通过监听器发送最终结果
                        listener?.onResult(finalResult)
                    }

                } catch (segmentException: Exception) {
                    Log.e(TAG, "处理音频片段失败", segmentException)
                    continue
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "音频处理失败", e)
            listener?.onError("音频处理失败: ${e.message}")
            resetBuffers()
        }
        
        return results
    }
    
    /**
     * 将音频数据分割为固定大小的片段
     */
    private fun splitAudioIntoSegments(audioData: FloatArray): List<FloatArray> {
        val segments = mutableListOf<FloatArray>()

        for (i in audioData.indices step SEGMENT_SIZE) {
            val endIndex = minOf(i + SEGMENT_SIZE, audioData.size)
            val segment = audioData.sliceArray(i until endIndex)

            if (segment.isNotEmpty()) {
                // 如果片段小于期望大小，用零填充到SEGMENT_SIZE
                val paddedSegment = if (segment.size < SEGMENT_SIZE) {
                    FloatArray(SEGMENT_SIZE) { index ->
                        if (index < segment.size) segment[index] else 0.0f
                    }
                } else {
                    segment
                }
                segments.add(paddedSegment)
            }
        }

        return segments
    }
    
    /**
     * 尝试预测识别 - 参考FunASR的_try_prediction逻辑
     */
    private fun tryPrediction(): ASRResult? {
        val currentTime = System.currentTimeMillis()

        // 如果连续失败次数过多，每5秒重置一次，给予重新尝试的机会
        if (consecutiveEmptyResults >= maxConsecutiveEmptyResults &&
            currentTime - lastResetTime > 5000) {
            Log.d(TAG, "连续失败次数过多，重置计数器给予重新尝试机会")
            consecutiveEmptyResults = maxConsecutiveEmptyResults - 2  // 重置到较低值但不是0
            lastResetTime = currentTime
        }

        // 预测条件 (参考FunASR的预测逻辑) - 放宽条件
        if (audioChunks.size < FINAL_CHUNK_SIZE &&   // 60
            predictionCounter == PREDICTION_INTERVAL &&  // 15
            audioChunks.size >= MIN_PREDICTION_CHUNKS &&  // 8
            consecutiveEmptyResults <= maxConsecutiveEmptyResults) {  // 改为 <= 允许达到上限时仍尝试
            Log.e(TAG, "预测识别1 audioChunks.size-{$audioChunks.size} predictionCounter-{$predictionCounter} consecutiveEmptyResults-{$consecutiveEmptyResults} maxConsecutiveEmptyResults-{$maxConsecutiveEmptyResults}")

            predictionCounter = 0

            try {
                // 获取当前识别结果 (参考FunASR的model.generate with cache)
                val result = recognizer.getResult(stream)
                val fullPrediction = result.text.trim()

                // 提取新增的预测内容，避免累积
                val prediction = extractNewPredictionContent(fullPrediction)

                // Log.d(TAG, "预测文本处理 - 完整预测: '$fullPrediction', 提取增量: '$prediction', 上次预测: '$lastFullPrediction'")

                // 应用FunASR的过滤规则
                if (prediction.isNotEmpty() && isValidPrediction(prediction)) {
                    lastPrediction = prediction
                    consecutiveEmptyResults = 0  // 成功时重置
                    lastResetTime = currentTime

                    // Log.d(TAG, "预测识别成功: '$prediction'")
                    return ASRResult(
                        text = prediction,
                        type = ResultType.PREVIEW,
                        confidence = calculateConfidence(prediction),
                        timestamp = System.currentTimeMillis()
                    )
                } else {
                    consecutiveEmptyResults++
                    Log.d(TAG, "预测结果无效或重复，跳过: '$prediction' (失败次数: $consecutiveEmptyResults)")
                }

            } catch (e: Exception) {
                Log.e(TAG, "预测识别失败", e)
                consecutiveEmptyResults++
            }

        } else if (predictionCounter >= PREDICTION_INTERVAL) {
            // 重置计数器，避免计数器无限增长
            Log.e(TAG, "预测识别2 audioChunks.size-{${audioChunks.size}} predictionCounter-{$predictionCounter} consecutiveEmptyResults-{$consecutiveEmptyResults} maxConsecutiveEmptyResults-{$maxConsecutiveEmptyResults}")
            predictionCounter = 0
        }

        return null
    }
    
    /**
     * 尝试端点检测 - 使用单模型的端点检测
     */
    private fun tryEndpointDetection(): ASRResult? {
        return try {
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastEndpointTime < endpointCooldownMs) {
                return null
            }
            
            val isEndpoint = recognizer.isEndpoint(stream)
            
            if (isEndpoint && audioChunks.isNotEmpty()) {
                Log.d(TAG, "检测到端点，进行最终识别")
                
                // 验证音频数据有效性
                val totalSamples = audioChunks.sumOf { it.size }
                val minRequiredSamples = (sampleRateInHz * 0.3).toInt()
                
                if (totalSamples < minRequiredSamples) {
                    Log.w(TAG, "端点检测：音频数据太少，跳过识别")
                    recognizer.reset(stream)
                    resetBuffers()
                    lastEndpointTime = currentTime
                    return null
                }
                
                // 使用单模型进行最终识别 (参考FunASR的is_final=True)
                val finalText = runFinalRecognition()
                
                if (finalText.isNotEmpty()) {
                    Log.d(TAG, "端点检测识别成功: '$finalText'")
                    
                    // 重置识别器状态
                    recognizer.reset(stream)
                    resetBuffers()
                    lastEndpointTime = currentTime
                    
                    ASRResult(
                        text = finalText,
                        type = ResultType.ENDPOINT,
                        timestamp = System.currentTimeMillis()
                    )
                } else {
                    recognizer.reset(stream)
                    resetBuffers()
                    lastEndpointTime = currentTime
                    null
                }
            } else null
            
        } catch (e: Exception) {
            Log.e(TAG, "端点检测失败", e)
            try {
                recognizer.reset(stream)
            } catch (resetException: Exception) {
                Log.e(TAG, "重置识别器失败", resetException)
            }
            resetBuffers()
            lastEndpointTime = System.currentTimeMillis()
            null
        }
    }
    
    /**
     * 尝试最终识别 - 参考FunASR的_try_final_recognition逻辑
     */
    private fun tryFinalRecognition(): ASRResult? {
        if (audioChunks.size >= FINAL_CHUNK_SIZE) {
            try {
                Log.d(TAG, "达到最终识别条件，chunks数量: ${audioChunks.size}")
                
                // 验证音频数据有效性
                val totalSamples = audioChunks.sumOf { it.size }
                val minRequiredSamples = (sampleRateInHz * 0.5).toInt()
                
                if (totalSamples < minRequiredSamples) {
                    Log.w(TAG, "最终识别：音频数据太少，跳过识别")
                    audioChunks.clear()
                    return null
                }
                
                // 使用单模型进行最终识别
                val finalText = runFinalRecognition()
                
                if (finalText.isNotEmpty()) {
                    Log.d(TAG, "最终识别成功: '$finalText'")

                    // 进行说话人识别
                    var speakerName = ""
                    var speakerConfidence = 0.0f
                    var hasSpeakerInfo = false

                    if (enableSpeakerIdentification && isSpeakerInitialized) {
                        val speakerResult = performSpeakerIdentification()
                        if (speakerResult != null) {
                            speakerName = speakerResult.name
                            speakerConfidence = speakerResult.confidence
                            hasSpeakerInfo = true

                            // 通知监听器说话人识别结果
                            listener?.onSpeakerIdentified(speakerResult)
                            Log.d(TAG, "说话人识别成功: $speakerName (置信度: $speakerConfidence)")
                        }else{
                            Log.d(TAG, "说话人识别失败: result==null")
                        }
                    }

                    audioChunks.clear()
                    
                    return ASRResult(
                        text = finalText,
                        type = ResultType.FINAL,
                        timestamp = System.currentTimeMillis(),
                        speakerName = speakerName,
                        speakerConfidence = speakerConfidence,
                        hasSpeakerInfo = hasSpeakerInfo
                    )
                } else {
                    Log.w(TAG, "最终识别结果为空")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "最终识别失败", e)
            }
            
            audioChunks.clear()
        }
        
        return null
    }

    /**
     * 运行最终识别 - 单模型实现
     */
    private fun runFinalRecognition(): String {
        return try {
            // 获取当前识别结果 (单模型直接获取最终结果)
            val result = recognizer.getResult(stream)
            val fullText = result.text.trim()

            Log.d(TAG, "单模型识别完成，完整结果: '$fullText'")

            // 提取新增内容，避免累积重复
            val newText = extractNewContent(fullText)

            Log.d(TAG, "提取的新内容: '$newText'")

            // 最终识别成功时重置预测失败计数器
            if (newText.isNotEmpty()) {
                consecutiveEmptyResults = 0
                lastResetTime = System.currentTimeMillis()
                Log.d(TAG, "最终识别成功，重置预测失败计数器")
            }

            // 立即重置流状态，防止下次累积
            recognizer.reset(stream)
            Log.d(TAG, "流状态已重置")

            // 应用结果后处理
            postProcessRecognitionResult(newText)

        } catch (e: Exception) {
            Log.e(TAG, "单模型识别失败", e)
            // 发生错误时也要重置流状态
            try {
                recognizer.reset(stream)
            } catch (resetException: Exception) {
                Log.e(TAG, "重置流失败", resetException)
            }
            ""
        }
    }

    /**
     * 提取新增内容，避免累积重复
     */
    private fun extractNewContent(fullText: String): String {
        if (fullText.isEmpty()) {
            return ""
        }

        // 如果没有之前的结果，返回完整文本
        if (lastFullResult.isEmpty()) {
            lastFullResult = fullText
            return fullText
        }

        // 如果当前结果与之前相同，返回空
        if (fullText == lastFullResult) {
            Log.d(TAG, "结果与上次相同，跳过")
            return ""
        }

        // 如果当前结果包含之前的结果，提取新增部分
        if (fullText.startsWith(lastFullResult)) {
            val newPart = fullText.substring(lastFullResult.length).trim()
            lastFullResult = fullText
            Log.d(TAG, "提取新增部分: '$newPart' (从完整结果: '$fullText')")
            return newPart
        }

        // 如果结果完全不同，可能是新的识别会话
        Log.d(TAG, "检测到新的识别会话，返回完整结果")
        lastFullResult = fullText
        return fullText
    }

    /**
     * 提取新增的预测内容，避免预测累积
     */
    private fun extractNewPredictionContent(fullPrediction: String): String {
        if (fullPrediction.isEmpty()) {
            return ""
        }

        // 如果没有之前的预测，返回完整预测
        if (lastFullPrediction.isEmpty()) {
            lastFullPrediction = fullPrediction
            return fullPrediction
        }

        // 如果当前预测与之前相同，返回空
        if (fullPrediction == lastFullPrediction) {
            return ""
        }

        // 如果当前预测包含之前的预测，提取新增部分
        if (fullPrediction.startsWith(lastFullPrediction)) {
            val newPart = fullPrediction.substring(lastFullPrediction.length).trim()
            lastFullPrediction = fullPrediction
            return newPart
        }

        // 如果预测完全不同，返回完整预测
        lastFullPrediction = fullPrediction
        return fullPrediction
    }

    /**
     * 验证预测结果是否有效 (参考FunASR智能边界检测)
     */
    private fun isValidPrediction(prediction: String): Boolean {
        if (prediction.isEmpty()) {
            Log.d(TAG, "预测结果为空，跳过")
            return false
        }

        // 基础长度检查
        if (prediction.length < MIN_SENTENCE_LENGTH) {
            Log.d(TAG, "预测结果太短(${prediction.length}字符)，跳过: '$prediction'")
            return false
        }
        if (prediction.length > MAX_PREDICTION_LENGTH) {
            Log.d(TAG, "预测结果太长(${prediction.length}字符)，跳过")
            return false
        }

        // 避免重复预测 - 加强检查
        if (prediction == lastPrediction) {
            Log.d(TAG, "预测结果与上次相同，跳过: '$prediction'")
            return false
        }

        // 避免预测结果是上次预测的子串或包含关系
        if (lastPrediction.isNotEmpty() &&
            (prediction.contains(lastPrediction) || lastPrediction.contains(prediction))) {
            Log.d(TAG, "预测结果与上次有重叠，跳过: '$prediction' vs '$lastPrediction'")
            return false
        }

        // 避免在分割标点处预测
        if (prediction.any { it in AVOID_SPLIT_PUNCTUATION }) {
            Log.d(TAG, "预测结果包含分割标点，跳过: '$prediction'")
            return false
        }

        // 避免以不完整指示词结尾的预测
        val lastChar = prediction.lastOrNull()
        if (lastChar != null && lastChar in INCOMPLETE_INDICATORS) {
            Log.d(TAG, "预测结果以不完整指示词结尾，跳过: '$prediction'")
            return false
        }

        // 检查是否为有意义的句子片段
        val isValid = if (prediction.length >= 5) {
            true
        } else {
            !prediction.all { it.isWhitespace() || it.isDigit() }
        }

        // if (isValid) {
        //     Log.d(TAG, "预测结果有效: '$prediction'")
        // } else {
        //     Log.d(TAG, "预测结果无意义，跳过: '$prediction'")
        // }

        return isValid
    }

    /**
     * 计算预测置信度
     */
    private fun calculateConfidence(prediction: String): Float {
        var confidence = 0.5f // 基础置信度

        // 长度加分
        confidence += minOf(prediction.length * 0.02f, 0.3f)

        // 句子完整性加分
        if (prediction.any { it in SENTENCE_END_PUNCTUATION }) {
            confidence += 0.2f
        }

        // 音频质量加分
        confidence += audioQualityScore * 0.2f

        return minOf(confidence, 1.0f)
    }

    /**
     * 识别结果后处理
     */
    private fun postProcessRecognitionResult(text: String): String {
        if (text.isEmpty()) return text

        var processedText = text

        // 移除多余的空格
        processedText = processedText.replace(Regex("\\s+"), " ")

        // 标点符号标准化
        processedText = processedText.replace("，，", "，")
        processedText = processedText.replace("。。", "。")
        processedText = processedText.replace("？？", "？")
        processedText = processedText.replace("！！", "！")

        // 移除开头的标点符号
        processedText = processedText.trimStart('，', '、', ',', ';', '；', '：', ':')

        return processedText.trim()
    }

    /**
     * 重置缓冲区
     */
    private fun resetBuffers() {
        audioChunks.clear()
        predictionCounter = 0
        lastPrediction = ""
        consecutiveEmptyResults = 0
        audioQualityScore = 1.0f
        lastFullResult = "" // 重置累积结果跟踪
        lastFullPrediction = "" // 重置累积预测跟踪
        lastResetTime = System.currentTimeMillis() // 重置时间跟踪
        Log.d(TAG, "缓冲区和状态已重置")
    }

    /**
     * 重置ASR状态 - 公共方法
     */
    fun reset() {
        resetBuffers()

        if (isInitialized) {
            try {
                stream.release()
                stream = recognizer.createStream()
            } catch (e: Exception) {
                Log.e(TAG, "重置流失败", e)
            }
        }

        Log.i(TAG, "单模型ASR状态已重置")
    }

    /**
     * 释放资源
     */
    fun release() {
        try {
            if (isInitialized) {
                stream.release()
                recognizer.release()
            }
            resetBuffers()
            isInitialized = false
            Log.i(TAG, "单模型ASR引擎资源已释放")
        } catch (e: Exception) {
            Log.e(TAG, "释放资源失败", e)
        }
    }

    /**
     * 初始化识别器 - 单模型配置
     */
    private fun initRecognizer() {
        // 选择一个高质量的在线模型 (可以处理预测和最终识别)
        val modelType = 8 // 使用Zipformer中文模型
        Log.i(TAG, "选择模型类型 $modelType 用于单模型识别")

        val config = OnlineRecognizerConfig(
            featConfig = com.k2fsa.sherpa.onnx.getFeatureConfig(sampleRate = sampleRateInHz, featureDim = 80),
            modelConfig = com.k2fsa.sherpa.onnx.getModelConfig(type = modelType)!!,
            endpointConfig = com.k2fsa.sherpa.onnx.getEndpointConfig(),
            enableEndpoint = true,
        )

        recognizer = OnlineRecognizer(
            assetManager = assetManager,
            config = config,
        )
    }


    // ==================== 说话人识别相关方法 ====================

    /**
     * 初始化说话人识别组件 - 使用全局SpeakerRecognition单例
     */
    private fun initSpeakerRecognition() {
        try {
            Log.i(TAG, "初始化说话人识别组件 - 使用全局单例")

            // 使用全局SpeakerRecognition单例
            SpeakerRecognition.initExtractor(assetManager)

            // 获取全局实例的引用
            speakerExtractor = SpeakerRecognition.extractor
            speakerManager = SpeakerRecognition.manager
            isSpeakerInitialized = true

            Log.i(TAG, "说话人识别组件初始化成功，特征维度: ${speakerExtractor!!.dim()}")

            // 恢复已保存的声纹数据
            restoreSavedSpeakers()

        } catch (e: Exception) {
            Log.e(TAG, "说话人识别组件初始化失败", e)
            speakerExtractor = null
            speakerManager = null
            isSpeakerInitialized = false
            throw e
        }
    }

    /**
     * 恢复已保存的声纹数据
     */
    private fun restoreSavedSpeakers() {
        speakerDataManager?.let { manager ->
            try {
                val savedSpeakers = manager.getAllSpeakers()
                Log.i(TAG, "开始恢复声纹数据，共 ${savedSpeakers.size} 个说话人")

                var successCount = 0
                for (speakerName in savedSpeakers) {
                    val audioSamples = manager.loadSpeaker(speakerName)
                    if (audioSamples != null && audioSamples.isNotEmpty()) {
                        val success = addSpeakerToManager(speakerName, audioSamples)
                        if (success) {
                            successCount++
                            Log.i(TAG, "恢复声纹成功: $speakerName (${audioSamples.size}个样本)")
                        } else {
                            Log.w(TAG, "恢复声纹失败: $speakerName")
                        }
                    } else {
                        Log.w(TAG, "声纹数据为空，跳过: $speakerName")
                    }
                }

                Log.i(TAG, "声纹数据恢复完成: $successCount/${savedSpeakers.size} 成功")

            } catch (e: Exception) {
                Log.e(TAG, "恢复声纹数据失败", e)
            }
        } ?: Log.w(TAG, "声纹数据管理器未初始化，跳过数据恢复")
    }

    /**
     * 执行说话人识别 - 使用全局SpeakerRecognition单例
     */
    private fun performSpeakerIdentification(): SpeakerInfo? {
        if (!isSpeakerInitialized) {
            Log.w(TAG, "说话人识别组件未初始化")
            return null
        }

        return try {
            // 合并音频数据
            val audioData = audioChunks.fold(mutableListOf<Float>()) { acc, chunk ->
                acc.addAll(chunk.toList())
                acc
            }.toFloatArray()

            if (audioData.isEmpty()) {
                Log.w(TAG, "说话人识别：音频数据为空")
                return null
            }

            // 使用全局SpeakerRecognition单例进行识别
            val stream = SpeakerRecognition.extractor.createStream()
            stream.acceptWaveform(audioData, sampleRateInHz)
            stream.inputFinished()

            if (SpeakerRecognition.extractor.isReady(stream)) {
                val embedding = SpeakerRecognition.extractor.compute(stream)
                val speakerName = SpeakerRecognition.manager.search(embedding, speakerThreshold)

                stream.release()

                if (speakerName.isNotEmpty()) {
                    Log.d(TAG, "说话人识别成功: $speakerName")
                    return SpeakerInfo(
                        name = speakerName,
                        confidence = speakerThreshold, // 实际应该计算真实置信度
                        embedding = embedding
                    )
                } else {
                    Log.d(TAG, "未识别到已知说话人")
                    return SpeakerInfo(
                        name = "未知说话人",
                        confidence = 0.0f,
                        embedding = embedding
                    )
                }
            } else {
                stream.release()
                Log.w(TAG, "说话人识别：音频流未准备就绪")
                return null
            }

        } catch (e: Exception) {
            Log.e(TAG, "说话人识别失败", e)
            null
        }
    }

    /**
     * 添加说话人 - 使用全局SpeakerRecognition单例
     */
    fun addSpeaker(name: String, audioSamples: FloatArray): Boolean {
        if (!isSpeakerInitialized) {
            Log.w(TAG, "说话人识别组件未初始化")
            listener?.onSpeakerRegistered(name, false)
            return false
        }

        return try {
            val stream = SpeakerRecognition.extractor.createStream()
            stream.acceptWaveform(audioSamples, sampleRateInHz)
            stream.inputFinished()

            if (SpeakerRecognition.extractor.isReady(stream)) {
                val embedding = SpeakerRecognition.extractor.compute(stream)
                val success = SpeakerRecognition.manager.add(name, embedding)

                stream.release()

                Log.i(TAG, "添加说话人 '$name': ${if (success) "成功" else "失败"}")
                listener?.onSpeakerRegistered(name, success)
                return success
            } else {
                stream.release()
                Log.w(TAG, "添加说话人失败：音频流未准备就绪")
                listener?.onSpeakerRegistered(name, false)
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "添加说话人失败", e)
            listener?.onSpeakerRegistered(name, false)
            false
        }
    }

    /**
     * 添加说话人 - 支持多个音频样本（提高准确性）
     */
    fun addSpeaker(name: String, audioSamplesList: List<FloatArray>): Boolean {
        if (!isSpeakerInitialized) {
            Log.w(TAG, "说话人识别组件未初始化")
            listener?.onSpeakerRegistered(name, false)
            return false
        }

        return try {
            // 先添加到内存中的SpeakerEmbeddingManager
            val success = addSpeakerToManager(name, audioSamplesList)

            if (success) {
                // 保存到持久化存储
                val persistSuccess = speakerDataManager?.saveSpeaker(name, audioSamplesList) ?: true
                if (!persistSuccess) {
                    Log.w(TAG, "声纹数据持久化失败，但内存注册成功: $name")
                }
                Log.i(TAG, "添加说话人 '$name' 成功 (内存: $success, 持久化: $persistSuccess)")
            }

            listener?.onSpeakerRegistered(name, success)
            success

        } catch (e: Exception) {
            Log.e(TAG, "添加说话人失败", e)
            listener?.onSpeakerRegistered(name, false)
            false
        }
    }

    /**
     * 添加说话人到内存管理器 - 内部方法
     */
    private fun addSpeakerToManager(name: String, audioSamplesList: List<FloatArray>): Boolean {
        return try {
            val embeddingList = mutableListOf<FloatArray>()

            // 为每个音频样本提取特征
            for (audioSamples in audioSamplesList) {
                val stream = SpeakerRecognition.extractor.createStream()
                stream.acceptWaveform(audioSamples, sampleRateInHz)
                stream.inputFinished()

                if (SpeakerRecognition.extractor.isReady(stream)) {
                    val embedding = SpeakerRecognition.extractor.compute(stream)
                    embeddingList.add(embedding)
                }
                stream.release()
            }

            if (embeddingList.isNotEmpty()) {
                val success = SpeakerRecognition.manager.add(name, embeddingList.toTypedArray())
                Log.d(TAG, "内存中添加说话人 '$name' (${embeddingList.size}个特征): ${if (success) "成功" else "失败"}")
                return success
            } else {
                Log.w(TAG, "添加说话人失败：无有效音频样本")
                return false
            }

        } catch (e: Exception) {
            Log.e(TAG, "内存中添加说话人失败", e)
            false
        }
    }

    /**
     * 删除说话人 - 使用全局SpeakerRecognition单例
     */
    fun removeSpeaker(name: String): Boolean {
        if (!isSpeakerInitialized) {
            Log.w(TAG, "说话人识别组件未初始化")
            listener?.onSpeakerRemoved(name, false)
            return false
        }

        return try {
            // 从内存中删除
            val memorySuccess = SpeakerRecognition.manager.remove(name)

            // 从持久化存储中删除
            val persistSuccess = speakerDataManager?.removeSpeaker(name) ?: true

            val success = memorySuccess && persistSuccess
            Log.i(TAG, "删除说话人 '$name': 内存($memorySuccess) 持久化($persistSuccess) 总体($success)")
            listener?.onSpeakerRemoved(name, success)
            success
        } catch (e: Exception) {
            Log.e(TAG, "删除说话人失败", e)
            listener?.onSpeakerRemoved(name, false)
            false
        }
    }

    /**
     * 获取所有说话人列表 - 使用全局SpeakerRecognition单例
     */
    fun getAllSpeakers(): Array<String> {
        return if (isSpeakerInitialized) {
            try {
                SpeakerRecognition.manager.allSpeakerNames()
            } catch (e: Exception) {
                Log.e(TAG, "获取说话人列表失败", e)
                emptyArray()
            }
        } else {
            emptyArray()
        }
    }

    /**
     * 获取说话人数量 - 使用全局SpeakerRecognition单例
     */
    fun getSpeakerCount(): Int {
        return if (isSpeakerInitialized) {
            try {
                SpeakerRecognition.manager.numSpeakers()
            } catch (e: Exception) {
                Log.e(TAG, "获取说话人数量失败", e)
                0
            }
        } else {
            0
        }
    }

    /**
     * 检查说话人是否存在 - 使用全局SpeakerRecognition单例
     */
    fun containsSpeaker(name: String): Boolean {
        return if (isSpeakerInitialized) {
            try {
                SpeakerRecognition.manager.contains(name)
            } catch (e: Exception) {
                Log.e(TAG, "检查说话人存在性失败", e)
                false
            }
        } else {
            false
        }
    }
}
