<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 原有颜色 -->
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>

    <!-- 新增优化主题颜色 -->
    <!-- 主色调 -->
    <color name="primary_blue">#2196F3</color>
    <color name="primary_blue_dark">#1976D2</color>
    <color name="primary_blue_light">#BBDEFB</color>

    <!-- 辅助色 -->
    <color name="accent_green">#4CAF50</color>
    <color name="accent_orange">#FF9800</color>
    <color name="accent_red">#F44336</color>

    <!-- 背景色 -->
    <color name="background_light">#F5F5F5</color>
    <color name="background_white">#FFFFFF</color>
    <color name="background_card">#FAFAFA</color>

    <!-- 文本颜色 -->
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="text_hint">#BDBDBD</color>
    <color name="text_preview">#1976D2</color>

    <!-- 状态颜色 -->
    <color name="status_ready">#4CAF50</color>
    <color name="status_recording">#F44336</color>
    <color name="status_processing">#FF9800</color>
    <color name="status_error">#F44336</color>

    <!-- 透明度变体 -->
    <color name="white_alpha_70">#B3FFFFFF</color>
    <color name="black_alpha_12">#1F000000</color>
    <color name="primary_alpha_12">#1F2196F3</color>

    <!-- 分割线和边框 -->
    <color name="divider_color">#E0E0E0</color>
    <color name="border_color">#EEEEEE</color>

    <!-- 特殊用途颜色 -->
    <color name="preview_background">#E3F2FD</color>
    <color name="results_background">#FAFAFA</color>
    <color name="count_background">#E8F5E8</color>
</resources>