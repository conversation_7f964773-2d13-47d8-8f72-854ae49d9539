package com.k2fsa.sherpa.onnx

import android.content.Context
import org.junit.Test
import org.junit.Assert.*
import org.junit.Before
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.MockitoAnnotations
import java.io.File

/**
 * 声纹数据管理器单元测试
 */
class SpeakerDataManagerTest {

    @Mock
    private lateinit var mockContext: Context

    private lateinit var speakerDataManager: SpeakerDataManager

    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        
        // 模拟Context行为
        val mockFilesDir = File.createTempFile("test", "dir").apply {
            delete()
            mkdirs()
        }
        `when`(mockContext.filesDir).thenReturn(mockFilesDir)
        `when`(mockContext.getSharedPreferences(anyString(), anyInt())).thenReturn(
            mock(android.content.SharedPreferences::class.java)
        )
        
        speakerDataManager = SpeakerDataManager(mockContext)
    }

    @Test
    fun testSaveAndLoadSpeaker() {
        // 准备测试数据
        val speakerName = "测试用户"
        val audioSamples = listOf(
            FloatArray(1000) { it * 0.001f },
            FloatArray(1000) { it * 0.002f }
        )

        // 测试保存
        val saveSuccess = speakerDataManager.saveSpeaker(speakerName, audioSamples)
        assertTrue("保存声纹应该成功", saveSuccess)

        // 测试加载
        val loadedSamples = speakerDataManager.loadSpeaker(speakerName)
        assertNotNull("加载的声纹数据不应为空", loadedSamples)
        assertEquals("样本数量应该匹配", audioSamples.size, loadedSamples!!.size)

        // 验证数据内容
        for (i in audioSamples.indices) {
            assertArrayEquals(
                "音频样本 $i 应该匹配",
                audioSamples[i],
                loadedSamples[i],
                0.0001f
            )
        }
    }

    @Test
    fun testRemoveSpeaker() {
        val speakerName = "待删除用户"
        val audioSamples = listOf(FloatArray(500) { it * 0.001f })

        // 先保存
        speakerDataManager.saveSpeaker(speakerName, audioSamples)
        assertTrue("声纹应该存在", speakerDataManager.containsSpeaker(speakerName))

        // 删除
        val removeSuccess = speakerDataManager.removeSpeaker(speakerName)
        assertTrue("删除应该成功", removeSuccess)
        assertFalse("声纹应该不存在", speakerDataManager.containsSpeaker(speakerName))
    }

    @Test
    fun testGetAllSpeakers() {
        val speakers = setOf("用户1", "用户2", "用户3")
        val audioSamples = listOf(FloatArray(100) { it * 0.001f })

        // 保存多个声纹
        speakers.forEach { speaker ->
            speakerDataManager.saveSpeaker(speaker, audioSamples)
        }

        // 验证获取所有声纹
        val allSpeakers = speakerDataManager.getAllSpeakers()
        assertEquals("声纹数量应该匹配", speakers.size, allSpeakers.size)
        assertTrue("应该包含所有声纹", allSpeakers.containsAll(speakers))
    }

    @Test
    fun testClearAllSpeakers() {
        // 保存一些声纹
        val speakers = listOf("用户A", "用户B")
        val audioSamples = listOf(FloatArray(100) { it * 0.001f })
        
        speakers.forEach { speaker ->
            speakerDataManager.saveSpeaker(speaker, audioSamples)
        }

        // 验证声纹存在
        assertTrue("应该有声纹存在", speakerDataManager.getAllSpeakers().isNotEmpty())

        // 清空所有声纹
        val clearSuccess = speakerDataManager.clearAllSpeakers()
        assertTrue("清空应该成功", clearSuccess)
        assertTrue("应该没有声纹", speakerDataManager.getAllSpeakers().isEmpty())
    }

    @Test
    fun testLoadNonExistentSpeaker() {
        val result = speakerDataManager.loadSpeaker("不存在的用户")
        assertNull("不存在的声纹应该返回null", result)
    }
}
