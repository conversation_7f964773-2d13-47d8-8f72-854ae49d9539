<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.SherpaOnnx2Pass" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent_green</item>
        <item name="colorSecondaryVariant">@color/accent_orange</item>
        <item name="colorOnSecondary">@color/white</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>

    <!-- 优化版主题 - 兼容Material Components -->
    <style name="Theme.SherpaOnnx2Pass.Optimized" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent_green</item>
        <item name="colorSecondaryVariant">@color/accent_orange</item>
        <item name="colorOnSecondary">@color/white</item>
        <!-- Surface colors -->
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_light</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/primary_blue</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>
</resources>
