package com.k2fsa.sherpa.onnx

import android.Manifest
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Typeface
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import kotlinx.coroutines.*
import org.json.JSONObject
import org.json.JSONArray
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 单模型ASR引擎测试Activity - 优化UI版本
 * 参考FunASR的单模型实现，使用一个模型完成所有识别任务
 * 复用优化版的UI布局和功能，但保持单模型架构
 */
class SingleModelActivity : AppCompatActivity(), SingleModelASREngine.ASRListener {

    companion object {
        private const val TAG = "SingleModelActivity"
        private const val REQUEST_RECORD_AUDIO_PERMISSION = 200
        private const val SAMPLE_RATE = 16000
        private const val BUFFER_SIZE = 1600 // 100ms at 16kHz
    }

    private val permissions: Array<String> = arrayOf(Manifest.permission.RECORD_AUDIO)

    // UI组件
    private lateinit var btnRecord: Button
    private lateinit var btnClear: Button
    private lateinit var btnSave: Button
    private lateinit var btnBack: Button
    private lateinit var btnSpeakerRegister: Button
    private lateinit var btnSpeakerManage: Button
    private lateinit var btnAsrOptimize: Button
    private lateinit var btnMeetingSummary: Button
    private lateinit var tvStatus: TextView
    private lateinit var tvResults: TextView
    private lateinit var tvStats: TextView

    // ASR引擎
    private lateinit var asrEngine: SingleModelASREngine
    private var isInitialized = false

    // 录音相关
    private var audioRecord: AudioRecord? = null
    private val isRecording = AtomicBoolean(false)
    private var recordingThread: Thread? = null

    // 结果管理
    private val recognitionResults = StringBuilder()
    private var wordCount = 0
    private var recognitionCount = 0
    private var sessionStartTime = 0L
    private val timeHandler = Handler(Looper.getMainLooper())
    private var timeUpdateRunnable: Runnable? = null

    // 实时预览状态管理
    private var isShowingPreview = false
    private var currentPreviewText = ""
    private var baseResultsText = ""
    private var previewTimestamp = ""
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            Log.i(TAG, "开始创建SingleModelActivity")

            // 使用优化版的布局
            setContentView(R.layout.activity_simple_test)

            // 请求权限
            ActivityCompat.requestPermissions(this, permissions, REQUEST_RECORD_AUDIO_PERMISSION)

            // 初始化UI
            initViews()

            // 初始化ASR引擎
            initASREngine()

            Log.i(TAG, "SingleModelActivity创建成功")

        } catch (e: Exception) {
            Log.e(TAG, "Activity创建失败", e)
            showToast("创建失败: ${e.message}")

            // 如果初始化失败，退出应用
            finish()
        }
    }
    
    private fun initViews() {
        try {
            btnRecord = findViewById(R.id.btn_record)
            btnClear = findViewById(R.id.btn_clear)
            btnSave = findViewById(R.id.btn_save)
            btnBack = findViewById(R.id.btn_back)
            btnSpeakerRegister = findViewById(R.id.btn_speaker_register)
            btnSpeakerManage = findViewById(R.id.btn_speaker_manage)
            btnAsrOptimize = findViewById(R.id.btn_asr_optimize)
            btnMeetingSummary = findViewById(R.id.btn_meeting_summary)
            tvStatus = findViewById(R.id.tv_status)
            tvResults = findViewById(R.id.tv_results)
            tvStats = findViewById(R.id.tv_stats)

            // 设置点击事件
            btnRecord.setOnClickListener { toggleRecording() }
            btnClear.setOnClickListener { clearResults() }
            btnSave.setOnClickListener { saveResults() }
            btnBack.setOnClickListener { goBack() }
            btnSpeakerRegister.setOnClickListener { showSpeakerRegistrationDialog() }
            btnSpeakerManage.setOnClickListener { showSpeakerList() }
            btnAsrOptimize.setOnClickListener { optimizeAsrContent() }
            btnMeetingSummary.setOnClickListener { generateMeetingSummary() }

            // 设置初始状态
            tvStatus.text = "正在初始化单模型ASR引擎..."
            tvResults.text = "点击开始录音按钮开始语音识别..."
            updateUI()

            Log.i(TAG, "UI初始化成功")

        } catch (e: Exception) {
            Log.e(TAG, "UI初始化失败", e)
            showToast("UI初始化失败: ${e.message}")
        }
    }
    
    /**
     * 初始化单模型ASR引擎
     */
    private fun initASREngine() {
        tvStatus.text = "正在初始化单模型ASR引擎..."

        Thread {
            try {
                // 初始化ASR引擎，传入Context以支持声纹持久化
                asrEngine = SingleModelASREngine(assets, SAMPLE_RATE, context = this@SingleModelActivity)
                asrEngine.setListener(this@SingleModelActivity)

                val success = asrEngine.initialize()

                runOnUiThread {
                    if (success) {
                        isInitialized = true
                        tvStatus.text = "✅ 单模型ASR引擎初始化成功 - 准备就绪"
                        Log.i(TAG, "单模型ASR引擎初始化成功")

                        // 显示恢复的声纹数量
                        val speakerCount = asrEngine.getSpeakerCount()
                        if (speakerCount > 0) {
                            showToast("已恢复 $speakerCount 个声纹")
                            Log.i(TAG, "已恢复 $speakerCount 个声纹")
                        }
                    } else {
                        tvStatus.text = "❌ 单模型ASR引擎初始化失败"
                        showToast("初始化失败")
                        Log.e(TAG, "单模型ASR引擎初始化失败")
                    }
                    updateUI()
                }

            } catch (e: Exception) {
                Log.e(TAG, "单模型ASR引擎初始化异常", e)
                runOnUiThread {
                    tvStatus.text = "❌ 单模型ASR引擎初始化异常: ${e.message}"
                    showToast("初始化失败: ${e.message}")
                    updateUI()
                }
            }
        }.start()
    }
    
    /**
     * 切换录音状态
     */
    private fun toggleRecording() {
        if (!isInitialized) {
            showToast("单模型ASR引擎未初始化")
            return
        }

        if (!isRecording.get()) {
            startRecording()
        } else {
            stopRecording()
        }
    }
    
    /**
     * 开始录音
     */
    private fun startRecording() {
        if (!initMicrophone()) {
            Log.e(TAG, "麦克风初始化失败")
            showToast("麦克风初始化失败")
            return
        }

        try {
            audioRecord?.startRecording()
            isRecording.set(true)
            sessionStartTime = System.currentTimeMillis()

            // 重置ASR引擎状态
            asrEngine.reset()

            // 更新UI
            tvStatus.text = "🎤 正在录音... (单模型识别)"
            updateUI()
            startTimeUpdate()

            // 开始录音线程
            recordingThread = Thread {
                recordAudio()
            }
            recordingThread?.start()

            Log.i(TAG, "开始录音 - 单模型模式")
            showToast("开始录音")

        } catch (e: Exception) {
            Log.e(TAG, "开始录音失败", e)
            showToast("录音失败: ${e.message}")
            stopRecording()
        }
    }
    
    /**
     * 停止录音
     */
    private fun stopRecording() {
        isRecording.set(false)

        try {
            audioRecord?.stop()
            audioRecord?.release()
            audioRecord = null

            recordingThread?.join(1000)
            recordingThread = null

            // 停止时间更新
            stopTimeUpdate()

            // 更新UI
            tvStatus.text = "✅ 录音已停止 - 准备就绪"
            updateUI()

            Log.i(TAG, "录音已停止")
            showToast("录音已停止")

        } catch (e: Exception) {
            Log.e(TAG, "停止录音失败", e)
        }
    }

    /**
     * 初始化麦克风
     */
    private fun initMicrophone(): Boolean {
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
            != PackageManager.PERMISSION_GRANTED) {
            showToast("需要录音权限")
            return false
        }

        val bufferSizeInBytes = AudioRecord.getMinBufferSize(
            SAMPLE_RATE,
            AudioFormat.CHANNEL_IN_MONO,
            AudioFormat.ENCODING_PCM_16BIT
        )

        audioRecord = AudioRecord(
            MediaRecorder.AudioSource.MIC,
            SAMPLE_RATE,
            AudioFormat.CHANNEL_IN_MONO,
            AudioFormat.ENCODING_PCM_16BIT,
            maxOf(bufferSizeInBytes, BUFFER_SIZE * 4)
        )

        return audioRecord?.state == AudioRecord.STATE_INITIALIZED
    }
    
    /**
     * 处理音频样本
     */
    private fun recordAudio() {
        Log.i(TAG, "开始处理音频样本")

        val buffer = ShortArray(BUFFER_SIZE)

        while (isRecording.get()) {
            try {
                val ret = audioRecord?.read(buffer, 0, buffer.size) ?: 0

                if (ret > 0) {
                    // 转换为Float数组 (归一化到[-1, 1])
                    val samples = FloatArray(ret) { buffer[it] / 32768.0f }

                    // 使用单模型ASR引擎处理音频
                    // 注意：结果处理通过ASRListener接口回调，避免重复处理
                    asrEngine.processAudio(samples)
                }

            } catch (e: Exception) {
                Log.e(TAG, "音频处理失败", e)
                break
            }
        }

        Log.i(TAG, "音频处理结束")
    }
    
    /**
     * 处理ASR识别结果 - 支持说话人识别
     */
    private fun handleASRResult(result: SingleModelASREngine.ASRResult) {
        Log.d(TAG, "收到ASR结果 - 类型: ${result.type}, 文本: '${result.text}', 时间戳: ${result.timestamp}")

        when (result.type) {
            SingleModelASREngine.ResultType.PREVIEW -> {
                // 显示实时预览
                showPreview(result.text)
                // Log.i(TAG, "处理预览结果: '${result.text}'")
            }
            SingleModelASREngine.ResultType.FINAL, SingleModelASREngine.ResultType.ENDPOINT -> {
                // 添加带说话人信息的最终结果
                addFinalResultWithSpeaker(result)
                // Log.i(TAG, "处理最终结果: '${result.text}'")
            }
        }
    }

    /**
     * 显示实时预览 - 直接更新主结果区域的当前行（累积显示）
     */
    private fun showPreview(text: String) {
        if (text.isBlank()) {
            // Log.d(TAG, "showPreview: 收到空白文本，跳过处理")
            return
        }

        // Log.d(TAG, "showPreview: 收到文本='$text', 当前预览状态=$isShowingPreview, 当前累积文本='$currentPreviewText'")

        // 如果不是正在显示预览，开始新的预览（生成新时间戳）
        if (!isShowingPreview) {
            previewTimestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())
            currentPreviewText = text  // 开始新预览，直接设置文本
            isShowingPreview = true
            // Log.d(TAG, "showPreview: 开始新预览 - 时间戳=$previewTimestamp, 初始文本='$currentPreviewText'")
        } else {
            // 正在显示预览，累积文本内容（ASR引擎返回的是增量文本）
            currentPreviewText += text  // 累积增量文本
            // Log.d(TAG, "showPreview: 累积预览文本 - 增量='$text', 累积后='$currentPreviewText'")
        }

        // 使用固定的时间戳构建预览行
        val previewLine = "[$previewTimestamp] $currentPreviewText"

        // 更新主结果区域：基础内容 + 当前累积预览行
        val displayText = if (baseResultsText.isNotEmpty()) {
            "$baseResultsText$previewLine"
        } else {
            previewLine
        }

        // Log.d(TAG, "showPreview: 更新显示文本='$displayText'")
        tvResults.text = displayText
    }

     /**
     * 添加最终结果 - 覆盖预览文本或添加新行，支持说话人信息
     */
    private fun addFinalResult(text: String) {
        if (text.isNotBlank()) {
            val timestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())
            val formattedResult = "[$timestamp] $text\n"

            Log.d(TAG, "addFinalResult: 收到最终结果='$text', 当前预览状态=$isShowingPreview, 当前预览文本='$currentPreviewText'")

            // 如果当前正在显示预览，则覆盖预览行
            if (isShowingPreview) {
                // 直接替换最后的预览内容
                recognitionResults.append(formattedResult)
                isShowingPreview = false
                currentPreviewText = ""
                previewTimestamp = ""
                Log.d(TAG, "addFinalResult: 覆盖预览内容，重置预览状态")
            } else {
                // 正常添加新行
                recognitionResults.append(formattedResult)
                Log.d(TAG, "addFinalResult: 正常添加新行")
            }

            // 更新基础结果文本
            baseResultsText = recognitionResults.toString()
            tvResults.text = baseResultsText

            Log.d(TAG, "addFinalResult: 更新基础结果文本，长度=${baseResultsText.length}")

            // 更新统计
            wordCount += text.length
            recognitionCount++
            updateStatistics()
        }
    }

    /**
     * 添加带说话人信息的最终结果
     */
    private fun addFinalResultWithSpeaker(result: SingleModelASREngine.ASRResult) {
        if (result.text.isNotBlank()) {
            val timestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())

            // 构建结果文本
            val formattedResult = "[$timestamp]-${result.speakerName}: ${result.text}\n"

            // Log.d(TAG, "addFinalResultWithSpeaker: 收到最终结果='${result.text}', 当前预览状态=$isShowingPreview")

            // 如果当前正在显示预览，则覆盖预览行
            if (isShowingPreview) {
                // 直接替换最后的预览内容
                recognitionResults.append(formattedResult)
                isShowingPreview = false
                currentPreviewText = ""
                previewTimestamp = ""
                // Log.d(TAG, "addFinalResultWithSpeaker: 覆盖预览内容，重置预览状态")
            } else {
                // 正常添加新行
                recognitionResults.append(formattedResult)
                // Log.d(TAG, "addFinalResultWithSpeaker: 正常添加新行")
            }

            // 更新基础结果文本
            baseResultsText = recognitionResults.toString()
            tvResults.text = baseResultsText

            // Log.d(TAG, "addFinalResultWithSpeaker: 更新基础结果文本，长度=${baseResultsText.length}")

            // 更新统计
            wordCount += result.text.length
            recognitionCount++
            updateStatistics()
        }
    }
    
    /**
     * 清空结果
     */
    private fun clearResults() {
        Log.d(TAG, "clearResults: 清空所有结果和预览状态")

        recognitionResults.clear()
        baseResultsText = ""
        isShowingPreview = false
        currentPreviewText = ""
        previewTimestamp = ""

        tvResults.text = "点击开始录音按钮开始语音识别..."
        wordCount = 0
        recognitionCount = 0
        updateStatistics()
        showToast("结果已清空")

        Log.d(TAG, "clearResults: 清空完成")
    }

    /**
     * 复制结果到剪贴板
     */
    private fun saveResults() {
        if (recognitionResults.isEmpty()) {
            showToast("没有可复制的内容")
            return
        }

        try {
            // 构建要复制的文本内容
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
            val content = buildString {
                append("单模型语音识别结果\n")
                append("生成时间: $timestamp\n")
                append("总字数: $wordCount\n")
                append("识别次数: $recognitionCount\n")
                append("======================================\n\n")
                append(recognitionResults.toString())
            }

            // 复制到剪贴板
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("ASR识别结果", content)
            clipboard.setPrimaryClip(clip)

            showToast("识别结果已复制到剪贴板")

        } catch (e: Exception) {
            Log.e(TAG, "复制结果失败", e)
            showToast("复制失败: ${e.message}")
        }
    }

    private fun goBack() {
        try {
            Log.i(TAG, "退出应用")
            finish()
        } catch (e: Exception) {
            Log.e(TAG, "退出失败", e)
            showToast("退出失败: ${e.message}")
        }
    }
    
    /**
     * 更新UI状态
     */
    private fun updateUI() {
        btnRecord.isEnabled = isInitialized
        btnRecord.text = if (isRecording.get()) "停止录音" else "开始录音"

        btnClear.isEnabled = !isRecording.get()
        btnSave.isEnabled = !isRecording.get() && recognitionResults.isNotEmpty()
        btnSpeakerRegister.isEnabled = isInitialized && !isRecording.get()
        btnSpeakerManage.isEnabled = isInitialized && !isRecording.get()
        btnAsrOptimize.isEnabled = !isRecording.get() && recognitionResults.isNotEmpty()
        btnMeetingSummary.isEnabled = !isRecording.get() && recognitionResults.isNotEmpty()
    }

    /**
     * 更新统计信息
     */
    private fun updateStatistics() {
        val elapsed = if (sessionStartTime > 0) {
            (System.currentTimeMillis() - sessionStartTime) / 1000
        } else 0

        val minutes = elapsed / 60
        val seconds = elapsed % 60

        tvStats.text = "字数: $wordCount | 识别次数: $recognitionCount | 时长: ${String.format("%02d:%02d", minutes, seconds)}"
    }

    /**
     * 开始时间更新
     */
    private fun startTimeUpdate() {
        timeUpdateRunnable = object : Runnable {
            override fun run() {
                if (isRecording.get()) {
                    updateStatistics()
                    timeHandler.postDelayed(this, 1000)
                }
            }
        }
        timeHandler.post(timeUpdateRunnable!!)
    }

    /**
     * 停止时间更新
     */
    private fun stopTimeUpdate() {
        timeUpdateRunnable?.let { timeHandler.removeCallbacks(it) }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    // ASRListener接口实现
    override fun onResult(result: SingleModelASREngine.ASRResult) {
        runOnUiThread {
            handleASRResult(result)
        }
    }

    override fun onError(error: String) {
        runOnUiThread {
            Log.e(TAG, "ASR错误: $error")
            tvStatus.text = "❌ 识别错误: $error"
            showToast("识别错误: $error")
        }
    }

    override fun onStatusChanged(status: String) {
        runOnUiThread {
            tvStatus.text = status
        }
    }

    override fun onSpeakerIdentified(speakerInfo: SingleModelASREngine.SpeakerInfo) {
        runOnUiThread {
            Log.d(TAG, "说话人识别结果: ${speakerInfo.name} (置信度: ${speakerInfo.confidence})")
            // 说话人识别结果已经在ASRResult中处理，这里只记录日志
        }
    }

    override fun onSpeakerRegistered(speakerName: String, success: Boolean) {
        runOnUiThread {
            val message = if (success) {
                "说话人 '$speakerName' 注册成功"
            } else {
                "说话人 '$speakerName' 注册失败"
            }
            Log.i(TAG, message)
            showToast(message)
        }
    }

    override fun onSpeakerRemoved(speakerName: String, success: Boolean) {
        runOnUiThread {
            val message = if (success) {
                "说话人 '$speakerName' 删除成功"
            } else {
                "说话人 '$speakerName' 删除失败"
            }
            Log.i(TAG, message)
            showToast(message)
        }
    }

    override fun onVadStatusChanged(isSpeech: Boolean) {
        runOnUiThread {
            Log.d(TAG, "VAD状态变化: ${if (isSpeech) "检测到语音" else "静音"}")
            // VAD状态变化，可以用于UI指示，暂时只记录日志
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // 停止录音
        if (isRecording.get()) {
            stopRecording()
        }

        // 停止说话人录音线程
        if (isSpeakerRecording) {
            isSpeakerRecording = false
            speakerRecordingThread?.let { thread ->
                try {
                    thread.join(1000) // 最多等待1秒
                } catch (e: InterruptedException) {
                    Log.w(TAG, "等待说话人录音线程结束被中断", e)
                }
            }
            speakerRecordingThread = null
        }

        // 释放ASR引擎
        if (isInitialized) {
            try {
                asrEngine.release()
            } catch (e: Exception) {
                Log.e(TAG, "释放ASR引擎失败", e)
            }
        }

        // 停止时间更新
        stopTimeUpdate()

        Log.i(TAG, "Activity已销毁")
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == REQUEST_RECORD_AUDIO_PERMISSION) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.i(TAG, "录音权限已授予")
                showToast("录音权限已授予")
            } else {
                Log.w(TAG, "录音权限被拒绝")
                showToast("录音权限被拒绝，部分功能可能无法使用")
            }
        }
    }

    // ==================== 说话人管理功能 ====================

    /**
     * 显示说话人注册对话框
     */
    private fun showSpeakerRegistrationDialog() {
        if (!isInitialized) {
            showToast("ASR引擎未初始化")
            return
        }

        // 创建自定义对话框布局
        val dialog = android.app.AlertDialog.Builder(this)
            .setTitle("注册说话人")
            .setMessage("请输入说话人姓名，然后录制3-5次音频样本以提高识别准确性")
            .create()

        // 创建输入框和按钮
        val layout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }

        val nameInput = android.widget.EditText(this).apply {
            hint = "请输入说话人姓名"
            setSingleLine(true)
        }

        val recordCountText = android.widget.TextView(this).apply {
            text = "录音次数: 0/5"
            textSize = 14f
            setPadding(0, 10, 0, 10)
        }

        val recordButton = android.widget.Button(this).apply {
            text = "开始录音"
        }

        val addButton = android.widget.Button(this).apply {
            text = "注册说话人"
            isEnabled = false
        }

        val statusText = android.widget.TextView(this).apply {
            text = "请输入姓名后点击录音，建议录制3-5次不同的音频样本"
            textSize = 12f
            setPadding(0, 10, 0, 0)
        }

        layout.addView(nameInput)
        layout.addView(recordCountText)
        layout.addView(recordButton)
        layout.addView(addButton)
        layout.addView(statusText)

        dialog.setView(layout)

        var isRecordingForSpeaker = false
        var speakerAudioSamples = mutableListOf<FloatArray>()
        var currentRecordingData = mutableListOf<Float>()

        recordButton.setOnClickListener {
            val speakerName = nameInput.text.toString().trim()

            if (speakerName.isEmpty()) {
                showToast("请输入说话人姓名")
                return@setOnClickListener
            }

            if (asrEngine.containsSpeaker(speakerName)) {
                showToast("说话人 '$speakerName' 已存在")
                return@setOnClickListener
            }

            if (!isRecordingForSpeaker) {
                // 开始录音
                startSpeakerRecording(recordButton, statusText, currentRecordingData)
                isRecordingForSpeaker = true
            } else {
                // 停止录音并保存样本
                stopSpeakerRecording(recordButton, statusText, currentRecordingData, speakerAudioSamples, recordCountText, addButton)
                isRecordingForSpeaker = false
            }
        }

        addButton.setOnClickListener {
            val speakerName = nameInput.text.toString().trim()
            if (speakerName.isNotEmpty() && speakerAudioSamples.isNotEmpty()) {
                registerSpeakerWithMultipleSamples(speakerName, speakerAudioSamples, dialog)
            }
        }

        dialog.setButton(android.app.AlertDialog.BUTTON_NEGATIVE, "取消") { _, _ ->
            dialog.dismiss()
        }

        dialog.show()
    }

    // 添加录音线程控制变量
    @Volatile
    private var speakerRecordingThread: Thread? = null
    @Volatile
    private var isSpeakerRecording = false

    /**
     * 开始说话人录音 - 改进版
     */
    private fun startSpeakerRecording(
        recordButton: android.widget.Button,
        statusText: android.widget.TextView,
        audioData: MutableList<Float>
    ) {
        if (!initMicrophone()) {
            showToast("麦克风初始化失败")
            return
        }

        try {
            audioRecord?.startRecording()
            recordButton.text = "停止录音"
            statusText.text = "正在录音... 请说话（建议录音3-5秒，说一句完整的话）"
            audioData.clear()

            // 设置录音标志
            isSpeakerRecording = true

            // 启动录音线程
            speakerRecordingThread = Thread {
                val buffer = ShortArray(BUFFER_SIZE)
                while (isSpeakerRecording) {
                    try {
                        val ret = audioRecord?.read(buffer, 0, buffer.size) ?: 0
                        if (ret > 0) {
                            // 使用同步块保护数据访问
                            synchronized(audioData) {
                                // 转换并收集音频数据
                                for (i in 0 until ret) {
                                    audioData.add(buffer[i] / 32768.0f)
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "说话人录音失败", e)
                        break
                    }
                }
            }
            speakerRecordingThread?.start()

        } catch (e: Exception) {
            Log.e(TAG, "开始说话人录音失败", e)
            showToast("录音失败: ${e.message}")
        }
    }

    /**
     * 停止说话人录音并保存样本 - 改进版
     */
    private fun stopSpeakerRecording(
        recordButton: android.widget.Button,
        statusText: android.widget.TextView,
        currentRecordingData: MutableList<Float>,
        speakerAudioSamples: MutableList<FloatArray>,
        recordCountText: android.widget.TextView,
        addButton: android.widget.Button
    ) {
        try {
            // 首先停止录音标志，确保录音线程停止
            isSpeakerRecording = false

            // 等待录音线程结束
            speakerRecordingThread?.let { thread ->
                try {
                    thread.join(1000) // 最多等待1秒
                } catch (e: InterruptedException) {
                    Log.w(TAG, "等待录音线程结束被中断", e)
                }
            }
            speakerRecordingThread = null

            audioRecord?.stop()
            audioRecord?.release()
            audioRecord = null

            recordButton.text = "开始录音"
            recordButton.isEnabled = true

            // 使用同步块安全访问数据并创建副本
            val audioDataCopy: FloatArray
            synchronized(currentRecordingData) {
                if (currentRecordingData.size < SAMPLE_RATE) { // 至少1秒音频
                    showToast("录音时间太短，请重新录音")
                    statusText.text = "录音时间太短，请重新录音（至少1秒）"
                    currentRecordingData.clear()
                    return
                }

                // 创建数据副本，避免并发修改异常
                audioDataCopy = currentRecordingData.toFloatArray()
                currentRecordingData.clear()
            }

            // 保存音频样本
            speakerAudioSamples.add(audioDataCopy)

            // 更新UI
            val recordCount = speakerAudioSamples.size
            recordCountText.text = "录音次数: $recordCount/5"
            statusText.text = "已录制 $recordCount 个样本。${if (recordCount >= 3) "可以注册了，" else "建议再录制 ${3 - recordCount} 个样本，"}或继续录制更多样本"

            // 启用注册按钮（至少需要1个样本，建议3个）
            addButton.isEnabled = recordCount >= 1

        } catch (e: Exception) {
            Log.e(TAG, "停止说话人录音失败", e)
            showToast("停止录音失败: ${e.message}")
        }
    }

    /**
     * 使用多个音频样本注册说话人
     */
    private fun registerSpeakerWithMultipleSamples(
        speakerName: String,
        audioSamples: List<FloatArray>,
        dialog: android.app.AlertDialog
    ) {
        Thread {
            val success = asrEngine.addSpeaker(speakerName, audioSamples)
            runOnUiThread {
                if (success) {
                    showToast("说话人 '$speakerName' 注册成功（使用了 ${audioSamples.size} 个样本）")
                    dialog.dismiss()
                } else {
                    showToast("说话人注册失败，请重试")
                }
            }
        }.start()
    }

    /**
     * 显示说话人列表
     */
    private fun showSpeakerList() {
        if (!isInitialized) {
            showToast("ASR引擎未初始化")
            return
        }

        val speakers = asrEngine.getAllSpeakers()
        val speakerCount = asrEngine.getSpeakerCount()

        val message = if (speakers.isEmpty()) {
            "当前没有已注册的说话人\n\n点击'注册说话人'按钮来添加新的说话人"
        } else {
            "已注册的说话人 (共 $speakerCount 个):\n\n" +
                    speakers.joinToString("\n") { "• $it" }
        }

        android.app.AlertDialog.Builder(this)
            .setTitle("说话人列表")
            .setMessage(message)
            .setPositiveButton("确定", null)
            .setNeutralButton("清空所有") { _, _ ->
                showClearAllSpeakersDialog(speakers)
            }
            .show()
    }

    /**
     * 显示清空所有说话人的确认对话框
     */
    private fun showClearAllSpeakersDialog(speakers: Array<String>) {
        if (speakers.isEmpty()) {
            showToast("没有可清空的说话人")
            return
        }

        android.app.AlertDialog.Builder(this)
            .setTitle("确认清空")
            .setMessage("确定要删除所有 ${speakers.size} 个已注册的说话人吗？\n\n此操作不可撤销。")
            .setPositiveButton("确定删除") { _, _ ->
                Thread {
                    var successCount = 0
                    for (speaker in speakers) {
                        if (asrEngine.removeSpeaker(speaker)) {
                            successCount++
                        }
                    }
                    runOnUiThread {
                        showToast("已删除 $successCount 个说话人")
                    }
                }.start()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    // ==================== AI 功能 ====================

    /**
     * ASR 内容优化
     */
    private fun optimizeAsrContent() {
        if (recognitionResults.isEmpty()) {
            showToast("没有语音识别内容可以优化")
            return
        }

        // 检查 API 密钥是否已配置
        if (!GeminiConfig.isApiKeyConfigured(this)) {
            showApiKeyConfigDialog()
            return
        }

        // 获取当前的语音转文字内容
        val originalContent = recognitionResults.toString()

        if (originalContent.trim().isEmpty()) {
            showToast("语音识别内容为空")
            return
        }

        // 显示加载对话框
        val loadingDialog = AlertDialog.Builder(this)
            .setTitle("ASR 内容优化")
            .setMessage("正在使用 AI 优化语音识别内容...\n\n" +
                    "优化内容：\n" +
                    "• 修正识别错误\n" +
                    "• 按说话人分段\n" +
                    "• 整合碎片句子\n" +
                    "• 优化标点符号")
            .setCancelable(false)
            .create()
        loadingDialog.show()

        // 在后台线程调用 Gemini API
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val optimizedContent = callGeminiAPIForOptimization(originalContent)

                runOnUiThread {
                    loadingDialog.dismiss()
                    showOptimizedContentDialog(optimizedContent, originalContent)
                }
            } catch (e: Exception) {
                runOnUiThread {
                    loadingDialog.dismiss()
                    Log.e(TAG, "ASR 内容优化失败", e)
                    showToast("ASR 内容优化失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 预处理原始内容，分析格式特点
     */
    private fun analyzeContentFormat(content: String): String {
        val lines = content.split("\n").filter { it.trim().isNotEmpty() }
        val hasSpeakers = lines.any { it.contains(Regex("\\[.*?\\]")) }
        val hasTimestamps = lines.any { it.contains(Regex("\\d{2}:\\d{2}")) }

        val analysisInfo = StringBuilder()
        if (hasSpeakers) {
            analysisInfo.append("- 检测到说话人标识，将按说话人分段整理\n")
        }
        if (hasTimestamps) {
            analysisInfo.append("- 检测到时间戳，将在优化时移除\n")
        }
        if (lines.size > 10) {
            analysisInfo.append("- 检测到多行内容，将整合为连贯段落\n")
        }

        return analysisInfo.toString()
    }

    /**
     * 调用 Gemini API 进行 ASR 内容优化
     */
    private suspend fun callGeminiAPIForOptimization(originalContent: String): String {
        return withContext(Dispatchers.IO) {
            val url = URL(GeminiConfig.getApiUrl(this@SingleModelActivity))

            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "POST"
            connection.setRequestProperty("Content-Type", "application/json")
            connection.doOutput = true

            // 分析原始内容格式
            val formatAnalysis = analyzeContentFormat(originalContent)

            // 构建专门的 ASR 优化提示词
            val prompt = """
                请对以下语音识别(ASR)内容进行优化和修正，要求：

                1. **语义准确性**：修正明显的识别错误，如同音字错误、语音识别常见错误
                2. **语言流畅性**：优化语句结构，使表达更加自然流畅
                3. **逻辑合理性**：整理语序，确保逻辑清晰、前后连贯
                4. **保持原意**：完全保持原始含义和重要信息，不添加、不删除关键内容
                5. **标点符号**：添加合适的标点符号，提高可读性
                6. **说话人分段**：将同一说话人的连续发言合并为一个段落

                **输出格式要求**：
                - 只合并**连续的**相同说话人发言，保持对话的时间顺序
                - 格式：[说话人姓名]：完整的连续发言内容。
                - 不同说话人之间用空行分隔
                - 如果同一说话人在对话中多次发言但被其他人打断，则分别保留为独立段落
                - 如果没有说话人标识，则按语义和逻辑分段，每段用空行分隔
                - 移除时间戳等技术信息
                - 避免原始的一句话一行的日志格式
                - 整合碎片化的句子为连贯段落

                **内容分析**：
                $formatAnalysis

                **处理策略**：
                - 将**连续的**同一说话人的多行发言合并为一个自然段落
                - 修正语音识别常见错误（同音字、语法错误等）
                - 保持口语化特点，不要过度书面化
                - 确保段落间逻辑连贯，语义完整
                - 严格按照时间顺序，不要重新排列说话人的发言顺序

                **示例说明**：
                原始内容：
                [张三] 大家好
                [张三] 今天我们讨论项目
                [李四] 好的
                [李四] 我先汇报
                [张三] 等一下
                [张三] 我还有补充

                正确输出：
                [张三]：大家好，今天我们讨论项目。

                [李四]：好的，我先汇报。

                [张三]：等一下，我还有补充。

                原始ASR内容：
                $originalContent

                请按照上述格式要求输出优化后的内容：
            """.trimIndent()

            val requestBody = JSONObject().apply {
                put("contents", JSONArray().apply {
                    put(JSONObject().apply {
                        put("parts", JSONArray().apply {
                            put(JSONObject().apply {
                                put("text", prompt)
                            })
                        })
                    })
                })
                put("generationConfig", JSONObject().apply {
                    put("temperature", 0.3) // 较低的温度，确保准确性
                    put("topK", 20)
                    put("topP", 0.8)
                    put("maxOutputTokens", 2048)
                })
            }

            // 发送请求
            val writer = OutputStreamWriter(connection.outputStream)
            writer.write(requestBody.toString())
            writer.flush()
            writer.close()

            // 读取响应
            val responseCode = connection.responseCode
            if (responseCode == HttpURLConnection.HTTP_OK) {
                val reader = BufferedReader(InputStreamReader(connection.inputStream))
                val response = reader.readText()
                reader.close()

                // 解析响应
                val jsonResponse = JSONObject(response)
                val candidates = jsonResponse.getJSONArray("candidates")
                if (candidates.length() > 0) {
                    val content = candidates.getJSONObject(0)
                        .getJSONObject("content")
                        .getJSONArray("parts")
                        .getJSONObject(0)
                        .getString("text")
                    return@withContext content.trim()
                } else {
                    throw Exception("API返回空结果")
                }
            } else {
                val errorReader = BufferedReader(InputStreamReader(connection.errorStream))
                val errorResponse = errorReader.readText()
                errorReader.close()
                throw Exception("API调用失败: $responseCode - $errorResponse")
            }
        }
    }

    /**
     * 显示优化后的内容对话框
     */
    private fun showOptimizedContentDialog(optimizedContent: String, originalContent: String) {
        val dialog = AlertDialog.Builder(this)
            .setTitle("✨ ASR 格式优化完成")
            .create()

        // 创建自定义布局
        val layout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }

        // 优化后内容显示区域
        val optimizedText = android.widget.TextView(this).apply {
            text = optimizedContent
            textSize = 14f
            setPadding(20, 20, 20, 20)
            setBackgroundResource(android.R.drawable.edit_text)
            isVerticalScrollBarEnabled = true
            maxLines = 15
        }

        // 按钮区域
        val buttonLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setPadding(0, 20, 0, 0)
        }

        val replaceBtn = android.widget.Button(this).apply {
            text = "替换原文"
            layoutParams = android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(0, 0, 5, 0)
            }
        }

        val copyOptimizedBtn = android.widget.Button(this).apply {
            text = "复制优化"
            layoutParams = android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(5, 0, 5, 0)
            }
        }

        val compareBtn = android.widget.Button(this).apply {
            text = "对比查看"
            layoutParams = android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(5, 0, 0, 0)
            }
        }

        buttonLayout.addView(replaceBtn)
        buttonLayout.addView(copyOptimizedBtn)
        buttonLayout.addView(compareBtn)

        layout.addView(optimizedText)
        layout.addView(buttonLayout)

        dialog.setView(layout)

        // 设置按钮点击事件
        replaceBtn.setOnClickListener {
            // 替换原始内容
            recognitionResults.clear()
            recognitionResults.append(optimizedContent)
            baseResultsText = recognitionResults.toString()
            tvResults.text = baseResultsText

            // 更新统计
            wordCount = optimizedContent.length
            updateStatistics()

            showToast("已替换为优化后的内容")
            dialog.dismiss()
        }

        copyOptimizedBtn.setOnClickListener {
            copyToClipboard("ASR优化内容", optimizedContent)
            showToast("优化内容已复制到剪贴板")
        }

        compareBtn.setOnClickListener {
            dialog.dismiss()
            showComparisonDialog(optimizedContent, originalContent)
        }

        dialog.setButton(AlertDialog.BUTTON_NEGATIVE, "关闭") { _, _ ->
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * 显示对比查看对话框
     */
    private fun showComparisonDialog(optimizedContent: String, originalContent: String) {
        val dialog = AlertDialog.Builder(this)
            .setTitle("📊 格式优化对比")
            .create()

        // 创建自定义布局
        val scrollView = ScrollView(this)
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(30, 30, 30, 30)
        }

        // 原始内容
        val originalLabel = android.widget.TextView(this).apply {
            text = "📝 原始内容（日志格式）："
            textSize = 16f
            setTypeface(null, Typeface.BOLD)
            setPadding(0, 0, 0, 10)
        }

        val originalText = android.widget.TextView(this).apply {
            text = originalContent
            textSize = 13f
            setPadding(15, 15, 15, 15)
            setBackgroundColor(0xFFF5F5F5.toInt())
            setTextColor(0xFF666666.toInt())
            maxLines = 8
            isVerticalScrollBarEnabled = true
        }

        // 分隔线
        val divider = View(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, 2
            ).apply {
                setMargins(0, 20, 0, 20)
            }
            setBackgroundColor(0xFFDDDDDD.toInt())
        }

        // 优化后内容
        val optimizedLabel = android.widget.TextView(this).apply {
            text = "✨ 优化后内容（分段格式）："
            textSize = 16f
            setTypeface(null, Typeface.BOLD)
            setPadding(0, 0, 0, 10)
        }

        val optimizedText = android.widget.TextView(this).apply {
            text = optimizedContent
            textSize = 14f
            setPadding(15, 15, 15, 15)
            setBackgroundColor(0xFFF0F8FF.toInt())
            setTextColor(0xFF333333.toInt())
            maxLines = 12
            isVerticalScrollBarEnabled = true
        }

        // 按钮区域
        val buttonLayout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            setPadding(0, 30, 0, 0)
        }

        val replaceBtn = android.widget.Button(this).apply {
            text = "使用优化版本"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(0, 0, 10, 0)
            }
        }

        val copyBtn = android.widget.Button(this).apply {
            text = "复制优化版本"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(10, 0, 0, 0)
            }
        }

        buttonLayout.addView(replaceBtn)
        buttonLayout.addView(copyBtn)

        // 格式说明
        val formatExplanation = android.widget.TextView(this).apply {
            text = "💡 优化说明：将原始的逐句日志格式转换为按连续说话人分段的自然段落格式。只合并连续的相同说话人发言，保持对话的时间顺序和逻辑结构。"
            textSize = 12f
            setPadding(10, 20, 10, 20)
            setBackgroundColor(0xFFFFFBE6.toInt())
            setTextColor(0xFF8B4513.toInt())
        }

        layout.addView(originalLabel)
        layout.addView(originalText)
        layout.addView(divider)
        layout.addView(optimizedLabel)
        layout.addView(optimizedText)
        layout.addView(formatExplanation)
        layout.addView(buttonLayout)

        scrollView.addView(layout)
        dialog.setView(scrollView)

        // 设置按钮点击事件
        replaceBtn.setOnClickListener {
            // 替换原始内容
            recognitionResults.clear()
            recognitionResults.append(optimizedContent)
            baseResultsText = recognitionResults.toString()
            tvResults.text = baseResultsText

            // 更新统计
            wordCount = optimizedContent.length
            updateStatistics()

            showToast("已替换为优化后的内容")
            dialog.dismiss()
        }

        copyBtn.setOnClickListener {
            copyToClipboard("ASR优化内容", optimizedContent)
            showToast("优化内容已复制到剪贴板")
        }

        dialog.setButton(AlertDialog.BUTTON_NEGATIVE, "关闭") { _, _ ->
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * 生成会议总结
     */
    private fun generateMeetingSummary() {
        if (recognitionResults.isEmpty()) {
            showToast("没有语音识别内容可以总结")
            return
        }

        // 检查 API 密钥是否已配置
        if (!GeminiConfig.isApiKeyConfigured(this)) {
            showApiKeyConfigDialog()
            return
        }

        // 获取当前的语音转文字内容
        val speechContent = recognitionResults.toString()

        if (speechContent.trim().isEmpty()) {
            showToast("语音识别内容为空")
            return
        }

        // 显示加载对话框
        val loadingDialog = AlertDialog.Builder(this)
            .setTitle("AI会议总结")
            .setMessage("正在调用Gemini 2.5大模型生成会议总结，请稍候...")
            .setCancelable(false)
            .create()
        loadingDialog.show()

        // 在后台线程调用Gemini API
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val summary = callGeminiAPI(speechContent)

                runOnUiThread {
                    loadingDialog.dismiss()
                    showMeetingSummaryDialog(summary, speechContent)
                }
            } catch (e: Exception) {
                runOnUiThread {
                    loadingDialog.dismiss()
                    Log.e(TAG, "生成会议总结失败", e)
                    showToast("生成会议总结失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 调用Gemini 2.5 API生成会议总结
     */
    private suspend fun callGeminiAPI(speechContent: String): String {
        return withContext(Dispatchers.IO) {
            val url = URL(GeminiConfig.getApiUrl(this@SingleModelActivity))

            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "POST"
            connection.setRequestProperty("Content-Type", "application/json")
            connection.doOutput = true

            // 构建请求体
            val prompt = """
                请对以下会议内容进行总结，要求：
                1. 提取关键要点和决策
                2. 整理讨论的主要话题
                3. 列出行动项目和责任人（如果有）
                4. 用中文回复，格式清晰

                会议内容：
                $speechContent
            """.trimIndent()

            val requestBody = JSONObject().apply {
                put("contents", JSONArray().apply {
                    put(JSONObject().apply {
                        put("parts", JSONArray().apply {
                            put(JSONObject().apply {
                                put("text", prompt)
                            })
                        })
                    })
                })
                put("generationConfig", JSONObject().apply {
                    put("temperature", 0.7)
                    put("topK", 40)
                    put("topP", 0.95)
                    put("maxOutputTokens", 2048)
                })
            }

            // 发送请求
            val writer = OutputStreamWriter(connection.outputStream)
            writer.write(requestBody.toString())
            writer.flush()
            writer.close()

            // 读取响应
            val responseCode = connection.responseCode
            if (responseCode == HttpURLConnection.HTTP_OK) {
                val reader = BufferedReader(InputStreamReader(connection.inputStream))
                val response = reader.readText()
                reader.close()

                // 解析响应
                val jsonResponse = JSONObject(response)
                val candidates = jsonResponse.getJSONArray("candidates")
                if (candidates.length() > 0) {
                    val content = candidates.getJSONObject(0)
                        .getJSONObject("content")
                        .getJSONArray("parts")
                        .getJSONObject(0)
                        .getString("text")
                    return@withContext content
                } else {
                    throw Exception("API返回空结果")
                }
            } else {
                val errorReader = BufferedReader(InputStreamReader(connection.errorStream))
                val errorResponse = errorReader.readText()
                errorReader.close()
                throw Exception("API调用失败: $responseCode - $errorResponse")
            }
        }
    }

    /**
     * 显示会议总结对话框
     */
    private fun showMeetingSummaryDialog(summary: String, originalContent: String) {
        val dialog = AlertDialog.Builder(this)
            .setTitle("🤖 AI会议总结")
            .create()

        // 创建自定义布局
        val layout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }

        // 总结内容显示区域
        val summaryText = android.widget.TextView(this).apply {
            text = summary
            textSize = 14f
            setPadding(20, 20, 20, 20)
            setBackgroundResource(android.R.drawable.edit_text)
            isVerticalScrollBarEnabled = true
            maxLines = 15
        }

        // 按钮区域
        val buttonLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setPadding(0, 20, 0, 0)
        }

        val copySummaryBtn = android.widget.Button(this).apply {
            text = "复制总结"
            layoutParams = android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(0, 0, 10, 0)
            }
        }

        val copyOriginalBtn = android.widget.Button(this).apply {
            text = "复制原文"
            layoutParams = android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(10, 0, 0, 0)
            }
        }

        buttonLayout.addView(copySummaryBtn)
        buttonLayout.addView(copyOriginalBtn)

        layout.addView(summaryText)
        layout.addView(buttonLayout)

        dialog.setView(layout)

        // 设置按钮点击事件
        copySummaryBtn.setOnClickListener {
            copyToClipboard("会议总结", summary)
            showToast("会议总结已复制到剪贴板")
        }

        copyOriginalBtn.setOnClickListener {
            copyToClipboard("会议原文", originalContent)
            showToast("会议原文已复制到剪贴板")
        }

        dialog.setButton(AlertDialog.BUTTON_NEGATIVE, "关闭") { _, _ ->
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * 复制文本到剪贴板
     */
    private fun copyToClipboard(label: String, text: String) {
        val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText(label, text)
        clipboard.setPrimaryClip(clip)
    }

    /**
     * 显示 API 密钥配置对话框
     */
    private fun showApiKeyConfigDialog() {
        val dialog = AlertDialog.Builder(this)
            .setTitle("配置 Gemini API 密钥")
            .setCancelable(false)
            .create()

        // 创建自定义布局
        val layout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }

        // 说明文本
        val instructionText = android.widget.TextView(this).apply {
            text = "请输入您的 Gemini API 密钥以使用 AI 会议总结功能。\n\n" +
                    "获取方式：\n" +
                    "1. 访问 https://makersuite.google.com/app/apikey\n" +
                    "2. 登录 Google 账号\n" +
                    "3. 创建新的 API 密钥\n" +
                    "4. 复制密钥并粘贴到下方输入框"
            textSize = 14f
            setPadding(0, 0, 0, 20)
        }

        // API 密钥输入框
        val apiKeyInput = EditText(this).apply {
            hint = "请输入 Gemini API 密钥"
            setSingleLine(true)
            inputType = android.text.InputType.TYPE_TEXT_VARIATION_PASSWORD
        }

        // 显示/隐藏密钥按钮
        val toggleVisibilityBtn = android.widget.Button(this).apply {
            text = "显示密钥"
            layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                topMargin = 10
            }
        }

        var isPasswordVisible = false
        toggleVisibilityBtn.setOnClickListener {
            isPasswordVisible = !isPasswordVisible
            if (isPasswordVisible) {
                apiKeyInput.inputType = android.text.InputType.TYPE_CLASS_TEXT
                toggleVisibilityBtn.text = "隐藏密钥"
            } else {
                apiKeyInput.inputType = android.text.InputType.TYPE_TEXT_VARIATION_PASSWORD
                toggleVisibilityBtn.text = "显示密钥"
            }
            apiKeyInput.setSelection(apiKeyInput.text.length)
        }

        // 按钮区域
        val buttonLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setPadding(0, 30, 0, 0)
        }

        val saveBtn = android.widget.Button(this).apply {
            text = "保存并使用"
            layoutParams = android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(0, 0, 10, 0)
            }
        }

        val cancelBtn = android.widget.Button(this).apply {
            text = "取消"
            layoutParams = android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(10, 0, 0, 0)
            }
        }

        buttonLayout.addView(saveBtn)
        buttonLayout.addView(cancelBtn)

        layout.addView(instructionText)
        layout.addView(apiKeyInput)
        layout.addView(toggleVisibilityBtn)
        layout.addView(buttonLayout)

        dialog.setView(layout)

        // 设置按钮点击事件
        saveBtn.setOnClickListener {
            val apiKey = apiKeyInput.text.toString().trim()
            if (apiKey.isEmpty()) {
                showToast("请输入 API 密钥")
                return@setOnClickListener
            }

            if (apiKey.length < 30) {
                showToast("API 密钥格式不正确，请检查")
                return@setOnClickListener
            }

            // 保存 API 密钥
            if (ApiKeyManager.saveApiKey(this, apiKey)) {
                showToast("API 密钥已保存")
                dialog.dismiss()
                // 重新尝试生成会议总结
                generateMeetingSummary()
            } else {
                showToast("保存 API 密钥失败")
            }
        }

        cancelBtn.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

}
