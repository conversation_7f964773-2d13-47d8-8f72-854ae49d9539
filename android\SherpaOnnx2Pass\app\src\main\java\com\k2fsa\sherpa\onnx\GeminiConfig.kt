package com.k2fsa.sherpa.onnx

import android.content.Context

/**
 * Gemini API 配置
 * 现在使用 ApiKeyManager 进行安全的密钥管理
 */
object GeminiConfig {
    // Gemini API 端点
    const val API_ENDPOINT = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent"

    // 检查 API 密钥是否已配置
    fun isApiKeyConfigured(context: Context): Boolean {
        return ApiKeyManager.hasValidApiKey(context)
    }

    // 获取完整的 API URL
    fun getApiUrl(context: Context): String {
        return ApiKeyManager.getApiUrl(context)
    }

    // 获取 API 密钥
    fun getApiKey(context: Context): String {
        return ApiKeyManager.getGeminiApiKey(context)
    }
}
